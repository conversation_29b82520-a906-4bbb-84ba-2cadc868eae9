#pragma once

class UCanvasNew : public UCanvas
{
public:
	//int padding;
	//float CanvasFontScale;

	void VARARGS WrappedPrintNew(ERenderStyle Style, INT& XL, INT& YL, UFont* Font, UBOOL Center, const char* Text);
	void WrappedStrLenfNew(UFont* Font, INT& XL, INT& YL, const TCHAR* Fmt, ...);
	void VARARGS WrappedPrintfNew(UFont* Font, UBOOL Center, const char* Fmt, ...);

	void execStrLenNew(FFrame& Stack, RESULT_DECL);
	void execTextSizeNew(FFrame& Stack, RESULT_DECL);
	void execDrawTextNew(<PERSON><PERSON>e& Stack, RESULT_DECL);
	void execDrawTextClippedNew(FFrame& Stack, RESULT_DECL);

	FLOAT getFontScale()
	{
		FLOAT FontScale = Y / 600.0f; // Viewport->SizeY
		//if (CanvasFontScale != 0)
		//{
		//	return FontScale * CanvasFontScale;
		//}
		return FontScale;
	}
};

static inline void DrawChar
(
	DWORD			Flags,
	UCanvasNew* Canvas,
	FTextureInfo& Info,
	INT				X,
	INT				Y,
	INT				XL,
	INT				YL,
	INT				U,
	INT				V,
	INT				UL,
	INT				VL,
	FPlane			Color
)
{
	guardSlow(DrawChar);

	// Reject.
	FSceneNode* Frame = Canvas->Frame;
	if (Frame == nullptr || Frame->Viewport == nullptr || Frame->Viewport->RenDev == nullptr)
	{
		return;
	}

	if (!(Flags & PF_Invisible) && X + XL > 0 && Y + YL > 0 && X < Frame->X && Y < Frame->Y && XL>0 && YL>0)
	{
		// Clip.
		if (X < 0) { INT C = X * UL / XL; U -= C; UL += C; XL += X; X = 0; }
		if (Y < 0) { INT C = Y * VL / YL; V -= C; VL += C; YL += Y; Y = 0; }
		if (XL > Frame->X - X) { UL += (Frame->X - X - XL) * UL / XL; XL = Frame->X - X; }
		if (YL > Frame->Y - Y) { VL += (Frame->Y - Y - YL) * VL / YL; YL = Frame->Y - Y; }

		// Draw.
		Frame->Viewport->RenDev->DrawTile(Frame, Info, X, Y, XL, YL, U, V, UL, VL, NULL, Canvas->Z, Color, FPlane(0, 0, 0, 0), Flags);
	}

	unguardSlow;
}

static inline void GetCharSize(UFont* Font, char InCh, INT& Width, INT& Height)
{
	guardSlow(GetCharSize);
	Width = 0;
	Height = 0;
	INT Ch = (unsigned char)Font->RemapChar(InCh);
	INT Page = Ch / Font->CharactersPerPage;
	INT Index = Ch - Page * Font->CharactersPerPage;
	if (Page < Font->Pages.Num() && Index < Font->Pages(Page).Characters.Num())
	{
		FFontCharacter& Char = Font->Pages(Page).Characters(Index);
		Width = Char.USize;
		Height = Char.VSize;
	}
	unguardSlow;
}

static INT DrawString
(
	DWORD			Flags,
	UCanvasNew* Canvas,
	UFont* Font,
	INT				DrawX,
	INT				DrawY,
	const char* Text,
	FPlane			Color,
	UBOOL			bClip,
	UBOOL			bHandleApersand,
	float			XScale = 1.0,
	float			YScale = 1.0
)
{
	if (!*Text)
		return 0;

	guardSlow(DrawString);

	// Font texture pages.
	FTextureInfo Infos[5];
	Infos[0].Texture = Infos[1].Texture = Infos[2].Texture = Infos[3].Texture = Infos[4].Texture = NULL;

	// Draw all characters in string.
	INT LineX = 0;
	INT bDrawUnderline = 0;
	INT UnderlineWidth = 0;
	for (INT i = 0; Text[i]; i++)
	{
		INT bUnderlineNext = 0;
		INT Ch = (unsigned char)Font->RemapChar(Text[i]);

		// Handle ampersand underlining.
		if (bHandleApersand)
		{
			if (bDrawUnderline)
				Ch = (unsigned char)Font->RemapChar('_');
			if (Text[i] == '&')
			{
				if (!Text[i + 1])
					break;
				if (Text[i + 1] != '&')
				{
					bUnderlineNext = 1;
					Ch = (unsigned char)Font->RemapChar(Text[i + 1]);
				}
			}
		}

		// Process character if it's valid.
	try_char:
		INT NewPage = Ch / Font->CharactersPerPage;
		UTexture* Tex;
		if (NewPage < Font->Pages.Num() && (Tex = Font->Pages(NewPage).Texture) != NULL)
		{
			INT        Index = Ch - NewPage * Font->CharactersPerPage;
			FFontPage& PageInfo = Font->Pages(NewPage);
			if (Index < PageInfo.Characters.Num())
			{
				// Get proper font page.
				FTextureInfo& Info = Infos[Min(NewPage, 4)];
				if (Info.Texture != Tex)
				{
					if (Info.Texture)
						Info.Texture->Unlock(Info);
					Tex->Lock(Info, Canvas->Viewport->CurrentTime, 0, Canvas->Viewport->RenDev);
				}
				FFontCharacter& Char = PageInfo.Characters(Index);

				// Try upper case.
				if (Char.USize == 0 && Ch >= 'a' && Ch <= 'z')
				{
					Ch += 'A' - 'a';
					goto try_char;
				}

				// Compute character width.
				FLOAT CharWidth;
				if (bDrawUnderline)
					CharWidth = Min(UnderlineWidth, Char.USize);
				else
					CharWidth = Char.USize;

				// Prepare for clipping.
				FLOAT X = LineX + DrawX;
				FLOAT Y = DrawY;
				FLOAT CU = Char.StartU;
				FLOAT CV = Char.StartV;
				FLOAT CUSize = CharWidth;
				FLOAT CVSize = Char.VSize;

				// Draw if it passes clip test.
				if
				(	(!bClip)
				||	( (X +(CUSize*XScale)) > 0 && X <= Canvas->ClipX && (Y + (CVSize*YScale)) >0 && Y <= Canvas->ClipY) )
				{

					if( bClip )
					{
						if( X        < 0.f           ) { CU-=X; CUSize+=X; X=0;  }
						if( Y        < 0.f           ) { CV-=Y; CVSize+=Y; Y=0;  }
						if( X+(CUSize*XScale) > Canvas->ClipX ) { CUSize=(INT) (Canvas->ClipX-X)/XScale; }
						if( Y+(CVSize*YScale) > Canvas->ClipY ) { CVSize=(INT) (Canvas->ClipY-Y)/YScale; } 
					}

					DrawChar(Flags, Canvas, Info, appRound(Canvas->OrgX + X), appRound(Canvas->OrgY + Y), CUSize * XScale, CVSize * YScale, CU, CV, CUSize, CVSize, Color);
					//Canvas->Frame->Viewport->RenDev->DrawTile(Canvas->Frame, Info, appRound(Canvas->OrgX + X), appRound(Canvas->OrgY + Y), CUSize * XScale, CVSize * YScale, CU, CV, CUSize, CVSize, NULL, Canvas->Z, Color, FPlane(0, 0, 0, 0), Flags);
				}

				// Update underline status.
				if (bDrawUnderline)
					CharWidth = UnderlineWidth;

				if( !bUnderlineNext )
					LineX += (INT)((FLOAT)(CharWidth + Canvas->SpaceX) * XScale);
				else
					UnderlineWidth = Char.USize;

				bDrawUnderline = bUnderlineNext;
			}
		}
	}

	// Unlock font pages.
	for (int i = 0; i < 5; i++)
		if (Infos[i].Texture)
			Infos[i].Texture->Unlock(Infos[i]);

	return LineX;
	unguardSlow;
}

void UCanvasNew::execStrLenNew(FFrame& Stack, RESULT_DECL)
{
	guard(UCanvasNew::execStrLenNew);

	P_GET_STR(InText);
	P_GET_FLOAT_REF(XL);
	P_GET_FLOAT_REF(YL);
	P_FINISH;

	INT XLi, YLi;
	INT OldCurX, OldCurY;
	OldCurX = (INT)CurX;
	OldCurY = (INT)CurY;
	CurX = 0;
	CurY = 0;
	WrappedStrLenfNew(Font, XLi, YLi, TEXT("%s"), *InText);
	CurY = OldCurY;
	CurX = OldCurX;
	*XL = XLi;
	*YL = YLi;

	unguard;
}

void UCanvasNew::execTextSizeNew(FFrame& Stack, RESULT_DECL)
{
	guard(UCanvasNew::execTextSize);
	P_GET_STR(InText);
	P_GET_FLOAT_REF(XL);
	P_GET_FLOAT_REF(YL);
	P_FINISH;

	if (!Font)
	{
		Stack.Logf(TEXT("TextSize: No font"));
		return;
	}

	INT XLi, YLi;
	INT OldCurX, OldCurY, OldClipX = ClipX;
	OldCurX = (INT)CurX;
	OldCurY = (INT)CurY;
	CurX = 0;
	CurY = 0;
	ClipX = 32767;
	WrappedPrintNew(STY_None, XLi, YLi, Font, 0, *InText);
	CurY = OldCurY;
	CurX = OldCurX;
	ClipX = OldClipX;
	*XL = XLi;
	*YL = YLi;
	unguardexec;
}

void UCanvasNew::execDrawTextNew(FFrame& Stack, RESULT_DECL)
{
	P_GET_STR(InText);
	P_GET_UBOOL_OPTX(CR, 1);
	P_FINISH;

	const UBOOL Clip = 0;
	const UBOOL Wrap = 1;

	if (!Font)
	{
		Stack.Logf(TEXT("DrawText: No font"));
		return;
	}
	INT XL = 0, YL = 0;
	if (Style != STY_None)
	{
		if (Wrap)
			WrappedPrintNew((ERenderStyle)Style, XL, YL, Font, bCenter, *InText);
		else {
			FPlane DrawColor = Color.Plane();
			DWORD PolyFlags
				= (PF_Masked | PF_RenderHint)
				| ((Style == STY_None) ? PF_Invisible
					: (Style == STY_Translucent) ? PF_Translucent
					: (Style == STY_Modulated) ? PF_Modulated
					: (Style == STY_AlphaBlend) ? (PF_Modulated | PF_Translucent)
					: 0);
			FLOAT scl = getFontScale();
			DrawString(PolyFlags, this, Font, (INT)CurX, (INT)CurY, *InText, DrawColor, Clip, 0, scl, scl);
		}
	}
	CurX += XL;
	CurYL = Max(CurYL, (FLOAT)YL);
	if (CR)
	{
		CurX = 0;
		CurY += CurYL;
		CurYL = 0;
	}
}

void UCanvasNew::execDrawTextClippedNew(FFrame& Stack, RESULT_DECL)
{
	guard(UCanvasNew::execDrawTextClippedNew);
	P_GET_STR(InText);
	P_GET_UBOOL_OPTX(CheckHotKey, 0);
	P_FINISH;

	if (!Font)
	{
		Stack.Logf(TEXT("DrawTextClipped: No font"));
		return;
	}

	//if ((Font == LargeFont || Font == BigFont) && appStricmp(UObject::GetLanguage(), TEXT("INT")))
	//	Font = MedFont;//BigFont;//!!
	//check(Font);

	// Generate flags.
	DWORD PolyFlags
		= (PF_NoSmooth | PF_Masked | PF_RenderHint)
		| ((Style == STY_None) ? PF_Invisible
			: (Style == STY_Translucent) ? PF_Translucent
			: (Style == STY_Modulated) ? PF_Modulated
			: (Style == STY_AlphaBlend) ? (PF_Modulated | PF_Translucent)
			: 0);

	FPlane DrawColor = Color.Plane();
	FLOAT scl = getFontScale();
	DrawString(PolyFlags, this, Font, (INT)CurX, (INT)CurY, *InText, DrawColor, 1, CheckHotKey, scl, scl);

	unguardexec;
}

void VARARGS UCanvasNew::WrappedPrintNew(ERenderStyle Style, INT& XL, INT& YL, UFont* Font, UBOOL Center, const char* Text)
{
	guard(UCanvasNew::WrappedPrintNew);

	if (ClipX < 0 || ClipY < 0 || !*Text || !Font)
	{
		XL = YL = 0;
		return;
	}
	//if ((Font == LargeFont || Font == BigFont) && appStricmp(UObject::GetLanguage(), TEXT("INT")))
	//	Font = MedFont;//BigFont;!!
	//check(Font);
	FPlane DrawColor = Color.Plane();

	// Generate flags.
	DWORD PolyFlags
		= (PF_NoSmooth | PF_Masked | PF_RenderHint)
		| ((Style == STY_None) ? PF_Invisible
			: (Style == STY_Translucent) ? PF_Translucent
			: (Style == STY_Modulated) ? PF_Modulated
			: (Style == STY_AlphaBlend) ? (PF_Modulated | PF_Translucent)
			: 0);

	// Process each word until the current line overflows.
	XL = YL = 0;
	float OrigX = CurX;
	do
	{
		INT iCleanWordEnd = 0, iTestWord;
		//INT TestXL = Center ? (INT)0 : (INT)CurX, CleanXL = 0;
		INT TestXL = (INT)CurX, CleanXL = 0;
		INT TestYL = 0, CleanYL = 0;
		UBOOL GotWord = 0;
		for (iTestWord = 0; Text[iTestWord] != 0 && Text[iTestWord] != '\n'; )
		{
			INT ChW, ChH;
			GetCharSize(Font, Text[iTestWord], ChW, ChH);
			if (ChW == 0)
				// Try upper case.
				if (Text[iTestWord] >= 'a' && Text[iTestWord] <= 'z')
					GetCharSize(Font, Text[iTestWord] + 'A' - 'a', ChW, ChH);
			FLOAT scl = getFontScale();
			ChW *= scl;
			ChH *= scl;
			TestXL += (INT)(ChW + SpaceX);
			TestYL = Max(TestYL, ChH + (INT)SpaceY);
			if (TestXL > ClipX)
				break;
			iTestWord++;
			UBOOL WordBreak = Text[iTestWord] == ' ' || Text[iTestWord] == '\n' || Text[iTestWord] == 0;
			if (WordBreak || !GotWord)
			{
				iCleanWordEnd = iTestWord;
				CleanXL = TestXL;
				CleanYL = TestYL;
				GotWord = GotWord || WordBreak;
			}
		}
		if (iCleanWordEnd == 0)
			break;

		// Sucessfully split this line, now draw it.
		if (Style != STY_None && OrgY + CurY < Frame->Y && OrgY + CurY + CleanYL>0)
		{
			FString TextLine(Text);
			INT LineX = Center ? (INT)(CurX + (ClipX - CleanXL) / 2) : (INT)(CurX);
			FLOAT scl = getFontScale();
			LineX += DrawString(PolyFlags, this, Font, LineX, (INT)CurY, *(TextLine.Left(iCleanWordEnd)), DrawColor, 0, 0, scl, scl);
			CurX = LineX;
		}

		// Update position.
		CurX = OrigX;
		CurY += CleanYL;
		YL += CleanYL;
		XL = Max(XL, CleanXL);
		Text += iCleanWordEnd;

		// Skip whitespace after word wrap.
		while (*Text == ' ')
			Text++;
	} while (*Text);

	unguardf((TEXT("(%s)"), Text));
}

void UCanvasNew::WrappedStrLenfNew(UFont* Font, INT& XL, INT& YL, const TCHAR* Fmt, ...)
{
	TCHAR Text[4096];
	GET_VARARGS(Text, ARRAY_COUNT(Text), Fmt);

	guard(UCanvasNew::WrappedStrLenfNew);
	WrappedPrintNew(STY_None, XL, YL, Font, 0, Text);
	unguard;
}

void VARARGS UCanvasNew::WrappedPrintfNew(UFont* Font, UBOOL Center, const char* Fmt, ...)
{
	char Text[4096];
	GET_VARARGS(Text, ARRAY_COUNT(Text), Fmt);

	guard(UCanvasNew::WrappedPrintf);
	INT XL = 0, YL = 0;
	WrappedPrintNew(STY_Normal, XL, YL, Font, Center, Text);
	unguard;
}

class TextDrawingPatch : public Patch
{
public:
	void ApplyPatch() override
	{
		// Font function hooks
		mem::TrampHook64(engineModuleBase + 0x642A0, union_cast<uint64_t>(&UCanvasNew::WrappedPrintNew), 5);
		mem::TrampHook64(engineModuleBase + 0x64F60, union_cast<uint64_t>(&UCanvasNew::execStrLenNew), 5);
		mem::TrampHook64(engineModuleBase + 0x66550, union_cast<uint64_t>(&UCanvasNew::execTextSizeNew), 5);
		mem::TrampHook64(engineModuleBase + 0x650F0, union_cast<uint64_t>(&UCanvasNew::execDrawTextNew), 5);
		mem::TrampHook64(engineModuleBase + 0x65D50, union_cast<uint64_t>(&UCanvasNew::execDrawTextClippedNew), 5);
	}
};